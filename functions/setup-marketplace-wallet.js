const { execSync } = require("child_process");

const MARKETPLACE_WALLET_MNEMONIC =
  "spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state";

function setFirebaseConfig() {
  try {
    console.log("Setting up marketplace wallet mnemonic in Firebase config...");

    const command = `firebase functions:config:set ton.marketplace_wallet_mnemonic="${MARKETPLACE_WALLET_MNEMONIC}"`;

    console.log(
      "Executing:",
      command.replace(MARKETPLACE_WALLET_MNEMONIC, "[HIDDEN]")
    );
    execSync(command, { stdio: "inherit" });

    console.log("✅ Marketplace wallet mnemonic configured successfully!");
    console.log("");
    console.log("To deploy the updated configuration, run:");
    console.log("  firebase deploy --only functions");
    console.log("");
    console.log("To verify the configuration, run:");
    console.log("  firebase functions:config:get");
  } catch (error) {
    console.error(
      "❌ Error setting up marketplace wallet mnemonic:",
      error.message
    );
    process.exit(1);
  }
}

// Run the setup
setFirebaseConfig();
